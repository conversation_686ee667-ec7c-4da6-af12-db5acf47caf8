/// Custom lint rules for DassoShu Reader cross-platform development
/// 
/// These rules enforce cross-platform best practices and prevent
/// platform-specific issues during development.

import 'package:analyzer/error/error.dart';
import 'package:analyzer/error/listener.dart';
import 'package:custom_lint_builder/custom_lint_builder.dart';

/// Plugin that provides all custom lint rules
class DassoReaderLintPlugin extends PluginBase {
  @override
  List<LintRule> getLintRules(CustomLintConfigs configs) => [
    AvoidPlatformSpecificImports(),
    PreferAdaptiveWidgets(),
    EnforceDesignSystemUsage(),
    ValidateChineseFontUsage(),
    CheckResponsiveDesign(),
    AvoidHardcodedPaths(),
    PreferAdaptiveNavigation(),
  ];
}

/// Rule: Avoid platform-specific imports
class AvoidPlatformSpecificImports extends DartLintRule {
  const AvoidPlatformSpecificImports() : super(code: _code);

  static const _code = LintCode(
    name: 'avoid_platform_specific_imports',
    problemMessage: 'Avoid importing platform-specific libraries directly. Use adaptive wrappers instead.',
    correctionMessage: 'Consider using PlatformAdaptations or AdaptiveWidgets.',
    errorSeverity: ErrorSeverity.ERROR,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addImportDirective((node) {
      final uri = node.uri.stringValue;
      if (uri == null) return;

      // Check for platform-specific imports
      final platformSpecificImports = [
        'dart:io',
        'package:flutter/cupertino.dart',
        'package:flutter/material.dart',
      ];

      if (platformSpecificImports.contains(uri)) {
        // Allow certain files to import these
        final filePath = resolver.path;
        if (_isAllowedToImportPlatformSpecific(filePath, uri)) return;

        reporter.reportErrorForNode(code, node);
      }
    });
  }

  bool _isAllowedToImportPlatformSpecific(String filePath, String uri) {
    // Allow platform adaptations and config files to import platform-specific code
    return filePath.contains('platform_adaptations.dart') ||
           filePath.contains('config/') ||
           filePath.contains('main.dart') ||
           (uri == 'dart:io' && filePath.contains('service/'));
  }
}

/// Rule: Prefer adaptive widgets over platform-specific ones
class PreferAdaptiveWidgets extends DartLintRule {
  const PreferAdaptiveWidgets() : super(code: _code);

  static const _code = LintCode(
    name: 'prefer_adaptive_widgets',
    problemMessage: 'Use adaptive widgets instead of platform-specific widgets.',
    correctionMessage: 'Replace with AdaptiveDialog, AdaptiveNavigation, etc.',
    errorSeverity: ErrorSeverity.WARNING,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addInstanceCreationExpression((node) {
      final typeName = node.staticType?.getDisplayString(withNullability: false);
      if (typeName == null) return;

      final platformSpecificWidgets = {
        'MaterialPageRoute': 'Use AdaptiveNavigation.push()',
        'CupertinoPageRoute': 'Use AdaptiveNavigation.push()',
        'AlertDialog': 'Use AdaptiveDialog.show()',
        'CupertinoAlertDialog': 'Use AdaptiveDialog.show()',
      };

      if (platformSpecificWidgets.containsKey(typeName)) {
        reporter.reportErrorForNode(code, node);
      }
    });
  }
}

/// Rule: Enforce design system usage
class EnforceDesignSystemUsage extends DartLintRule {
  const EnforceDesignSystemUsage() : super(code: _code);

  static const _code = LintCode(
    name: 'enforce_design_system_usage',
    problemMessage: 'Use DesignSystem constants instead of hardcoded values.',
    correctionMessage: 'Replace with DesignSystem.spaceM, DesignSystem.radiusM, etc.',
    errorSeverity: ErrorSeverity.ERROR,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addInstanceCreationExpression((node) {
      final source = node.toSource();
      
      // Check for hardcoded EdgeInsets
      if (source.contains('EdgeInsets.all(') || 
          source.contains('EdgeInsets.symmetric(')) {
        final hasHardcodedValue = RegExp(r'\d+\.?\d*').hasMatch(source);
        if (hasHardcodedValue && !source.contains('DesignSystem')) {
          reporter.reportErrorForNode(code, node);
        }
      }

      // Check for hardcoded BorderRadius
      if (source.contains('BorderRadius.circular(')) {
        final hasHardcodedValue = RegExp(r'\d+\.?\d*').hasMatch(source);
        if (hasHardcodedValue && !source.contains('DesignSystem')) {
          reporter.reportErrorForNode(code, node);
        }
      }
    });
  }
}

/// Rule: Validate Chinese font usage
class ValidateChineseFontUsage extends DartLintRule {
  const ValidateChineseFontUsage() : super(code: _code);

  static const _code = LintCode(
    name: 'validate_chinese_font_usage',
    problemMessage: 'Use chinese_font_library for proper Chinese text rendering.',
    correctionMessage: 'Import chinese_font_library and use appropriate Chinese fonts.',
    errorSeverity: ErrorSeverity.WARNING,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addStringLiteral((node) {
      final value = node.stringValue;
      if (value == null) return;

      // Check if string contains Chinese characters
      final hasChineseChars = RegExp(r'[\u4e00-\u9fff]').hasMatch(value);
      if (!hasChineseChars) return;

      // Check if file imports chinese_font_library
      final unit = node.thisOrAncestorOfType<CompilationUnit>();
      if (unit == null) return;

      final hasChineseFontImport = unit.directives
          .whereType<ImportDirective>()
          .any((import) => import.uri.stringValue?.contains('chinese_font_library') == true);

      if (!hasChineseFontImport) {
        reporter.reportErrorForNode(code, node);
      }
    });
  }
}

/// Rule: Check responsive design implementation
class CheckResponsiveDesign extends DartLintRule {
  const CheckResponsiveDesign() : super(code: _code);

  static const _code = LintCode(
    name: 'check_responsive_design',
    problemMessage: 'Consider using ResponsiveSystem for tablet/phone adaptations.',
    correctionMessage: 'Use ResponsiveSystem.isTablet() or DesignSystem.getAdaptivePadding().',
    errorSeverity: ErrorSeverity.INFO,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addMethodInvocation((node) {
      final source = node.toSource();
      
      if (source.contains('MediaQuery.of(context).size') &&
          !source.contains('ResponsiveSystem') &&
          !source.contains('DesignSystem.getAdaptive')) {
        reporter.reportErrorForNode(code, node);
      }
    });
  }
}

/// Rule: Avoid hardcoded file paths
class AvoidHardcodedPaths extends DartLintRule {
  const AvoidHardcodedPaths() : super(code: _code);

  static const _code = LintCode(
    name: 'avoid_hardcoded_paths',
    problemMessage: 'Use path.join() for cross-platform file paths.',
    correctionMessage: 'Import package:path/path.dart and use path.join().',
    errorSeverity: ErrorSeverity.WARNING,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addStringLiteral((node) {
      final value = node.stringValue;
      if (value == null) return;

      // Check for hardcoded path separators
      if (value.contains('/') || value.contains('\\')) {
        // Allow certain patterns (URLs, etc.)
        if (value.startsWith('http') || value.startsWith('assets/')) return;
        
        reporter.reportErrorForNode(code, node);
      }
    });
  }
}

/// Rule: Prefer adaptive navigation
class PreferAdaptiveNavigation extends DartLintRule {
  const PreferAdaptiveNavigation() : super(code: _code);

  static const _code = LintCode(
    name: 'prefer_adaptive_navigation',
    problemMessage: 'Use adaptive navigation for cross-platform consistency.',
    correctionMessage: 'Replace with AdaptiveNavigation.push() or similar.',
    errorSeverity: ErrorSeverity.ERROR,
  );

  @override
  void run(
    CustomLintResolver resolver,
    ErrorReporter reporter,
    CustomLintContext context,
  ) {
    context.registry.addMethodInvocation((node) {
      final methodName = node.methodName.name;
      
      if (methodName == 'push' || methodName == 'pushReplacement') {
        final source = node.toSource();
        if (source.contains('MaterialPageRoute') || 
            source.contains('CupertinoPageRoute')) {
          reporter.reportErrorForNode(code, node);
        }
      }
    });
  }
}
