/// Global test configuration for DassoShu Reader
/// 
/// Provides centralized configuration for all tests including platform-specific
/// settings, mock configurations, and test environment setup.

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';

/// Global test configuration class
class TestConfig {
  static const String _platformEnvKey = 'PLATFORM_TEST_MODE';
  static const String _coverageEnvKey = 'TEST_COVERAGE_ENABLED';
  static const String _goldenUpdateEnvKey = 'GOLDEN_TEST_UPDATE';

  /// Current platform being tested
  static TestPlatform get currentPlatform {
    final platformEnv = Platform.environment[_platformEnvKey]?.toLowerCase();
    switch (platformEnv) {
      case 'android':
        return TestPlatform.android;
      case 'ios':
        return TestPlatform.ios;
      case 'both':
        return TestPlatform.both;
      default:
        return TestPlatform.both;
    }
  }

  /// Whether test coverage is enabled
  static bool get isCoverageEnabled {
    return Platform.environment[_coverageEnvKey]?.toLowerCase() == 'true';
  }

  /// Whether golden files should be updated
  static bool get shouldUpdateGoldens {
    return Platform.environment[_goldenUpdateEnvKey]?.toLowerCase() == 'true';
  }

  /// Test timeout duration
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration longTimeout = Duration(minutes: 2);
  static const Duration shortTimeout = Duration(seconds: 10);

  /// Test data paths
  static const String testDataPath = 'test/fixtures/';
  static const String goldenPath = 'test/golden/';
  static const String mockDataPath = 'test/fixtures/mock_data/';

  /// Platform-specific test settings
  static TestPlatformSettings get platformSettings {
    switch (currentPlatform) {
      case TestPlatform.android:
        return TestPlatformSettings.android();
      case TestPlatform.ios:
        return TestPlatformSettings.ios();
      case TestPlatform.both:
        return TestPlatformSettings.both();
    }
  }

  /// Initialize test environment
  static void initialize() {
    // Set up test environment
    debugDefaultTargetPlatformOverride = platformSettings.targetPlatform;
    
    // Configure test timeouts
    testWidgets.timeout = defaultTimeout;
    
    // Set up golden file testing
    if (shouldUpdateGoldens) {
      autoUpdateGoldenFiles = true;
    }
  }

  /// Clean up test environment
  static void cleanup() {
    debugDefaultTargetPlatformOverride = null;
  }
}

/// Test platform enumeration
enum TestPlatform {
  android,
  ios,
  both,
}

/// Platform-specific test settings
class TestPlatformSettings {
  final TargetPlatform? targetPlatform;
  final Size screenSize;
  final double devicePixelRatio;
  final String goldenSuffix;
  final Map<String, dynamic> platformSpecificConfig;

  const TestPlatformSettings({
    required this.targetPlatform,
    required this.screenSize,
    required this.devicePixelRatio,
    required this.goldenSuffix,
    required this.platformSpecificConfig,
  });

  /// Android test settings
  factory TestPlatformSettings.android() {
    return const TestPlatformSettings(
      targetPlatform: TargetPlatform.android,
      screenSize: Size(412, 892), // Pixel 5 dimensions
      devicePixelRatio: 2.75,
      goldenSuffix: '_android',
      platformSpecificConfig: {
        'useMaterial3': true,
        'materialVersion': 3,
        'scrollPhysics': 'ClampingScrollPhysics',
        'pageTransition': 'MaterialPageRoute',
      },
    );
  }

  /// iOS test settings
  factory TestPlatformSettings.ios() {
    return const TestPlatformSettings(
      targetPlatform: TargetPlatform.iOS,
      screenSize: Size(414, 896), // iPhone 11 dimensions
      devicePixelRatio: 2.0,
      goldenSuffix: '_ios',
      platformSpecificConfig: {
        'useCupertino': true,
        'scrollPhysics': 'BouncingScrollPhysics',
        'pageTransition': 'CupertinoPageRoute',
      },
    );
  }

  /// Both platforms (default to Android for rendering)
  factory TestPlatformSettings.both() {
    return TestPlatformSettings.android().copyWith(
      goldenSuffix: '_cross_platform',
    );
  }

  /// Copy with modifications
  TestPlatformSettings copyWith({
    TargetPlatform? targetPlatform,
    Size? screenSize,
    double? devicePixelRatio,
    String? goldenSuffix,
    Map<String, dynamic>? platformSpecificConfig,
  }) {
    return TestPlatformSettings(
      targetPlatform: targetPlatform ?? this.targetPlatform,
      screenSize: screenSize ?? this.screenSize,
      devicePixelRatio: devicePixelRatio ?? this.devicePixelRatio,
      goldenSuffix: goldenSuffix ?? this.goldenSuffix,
      platformSpecificConfig: platformSpecificConfig ?? this.platformSpecificConfig,
    );
  }
}

/// Test categories for organization
enum TestCategory {
  unit,
  widget,
  integration,
  platform,
  golden,
  performance,
}

/// Test severity levels
enum TestSeverity {
  critical,    // Must pass for release
  important,   // Should pass for release
  optional,    // Nice to have
}

/// Test metadata for better organization
class TestMetadata {
  final TestCategory category;
  final TestSeverity severity;
  final List<TestPlatform> supportedPlatforms;
  final Duration? customTimeout;
  final Map<String, dynamic> tags;

  const TestMetadata({
    required this.category,
    this.severity = TestSeverity.important,
    this.supportedPlatforms = const [TestPlatform.both],
    this.customTimeout,
    this.tags = const {},
  });

  /// Check if test should run on current platform
  bool shouldRunOnCurrentPlatform() {
    final current = TestConfig.currentPlatform;
    return supportedPlatforms.contains(current) || 
           supportedPlatforms.contains(TestPlatform.both);
  }
}

/// Common test utilities
class TestUtils {
  /// Create a test widget with proper theming
  static Widget createTestWidget(Widget child) {
    return MaterialApp(
      theme: ThemeData(useMaterial3: true),
      home: Scaffold(body: child),
    );
  }

  /// Pump and settle with platform-specific timing
  static Future<void> pumpAndSettleWithPlatform(WidgetTester tester) async {
    await tester.pumpAndSettle(
      TestConfig.platformSettings.targetPlatform == TargetPlatform.iOS
          ? const Duration(milliseconds: 300)
          : const Duration(milliseconds: 200),
    );
  }

  /// Generate golden file name with platform suffix
  static String goldenFileName(String baseName) {
    return '$baseName${TestConfig.platformSettings.goldenSuffix}.png';
  }
}
