/// Mock providers for Riverpod testing in DassoShu Reader
/// 
/// Provides mock implementations of all major providers for isolated testing
/// without external dependencies.

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';

// Import your actual providers here
// import 'package:dasso_reader/providers/book_list.dart';
// import 'package:dasso_reader/providers/ai_chat.dart';
// import 'package:dasso_reader/providers/bookmark.dart';
// import 'package:dasso_reader/providers/fonts.dart';
// import 'package:dasso_reader/providers/hsk_providers.dart';
// import 'package:dasso_reader/providers/statistic_data.dart';
// import 'package:dasso_reader/providers/storage_info.dart';

/// Mock data for testing
class MockData {
  // Mock book data
  static const mockBooks = [
    // Add mock book objects here
  ];

  // Mock user preferences
  static const mockPreferences = {
    'theme': 'light',
    'fontSize': 16.0,
    'language': 'en',
  };

  // Mock AI chat messages
  static const mockChatMessages = [
    // Add mock chat messages here
  ];

  // Mock bookmarks
  static const mockBookmarks = [
    // Add mock bookmarks here
  ];

  // Mock HSK characters
  static const mockHskCharacters = [
    // Add mock HSK characters here
  ];

  // Mock statistics
  static const mockStatistics = {
    'totalReadingTime': 3600, // seconds
    'booksRead': 5,
    'charactersLearned': 150,
  };
}

/// Mock provider overrides for testing
class MockProviders {
  /// Get all mock provider overrides
  static List<Override> get all => [
    ...bookProviders,
    ...aiProviders,
    ...userProviders,
    ...systemProviders,
  ];

  /// Book-related provider overrides
  static List<Override> get bookProviders => [
    // bookListProvider.overrideWith((ref) => MockData.mockBooks),
    // currentBookProvider.overrideWith((ref) => MockData.mockBooks.first),
    // bookmarkProvider.overrideWith((ref) => MockData.mockBookmarks),
  ];

  /// AI-related provider overrides
  static List<Override> get aiProviders => [
    // aiChatProvider.overrideWith((ref) => MockData.mockChatMessages),
    // aiServiceProvider.overrideWith((ref) => MockAiService()),
  ];

  /// User preference provider overrides
  static List<Override> get userProviders => [
    // userPreferencesProvider.overrideWith((ref) => MockData.mockPreferences),
    // fontProvider.overrideWith((ref) => MockFontProvider()),
  ];

  /// System provider overrides
  static List<Override> get systemProviders => [
    // storageInfoProvider.overrideWith((ref) => MockStorageInfo()),
    // statisticDataProvider.overrideWith((ref) => MockData.mockStatistics),
  ];

  /// Create custom override for specific test scenarios
  static List<Override> custom({
    dynamic bookList,
    dynamic aiChat,
    dynamic userPrefs,
    dynamic storage,
  }) {
    final overrides = <Override>[];

    // if (bookList != null) {
    //   overrides.add(bookListProvider.overrideWith((ref) => bookList));
    // }
    // if (aiChat != null) {
    //   overrides.add(aiChatProvider.overrideWith((ref) => aiChat));
    // }
    // if (userPrefs != null) {
    //   overrides.add(userPreferencesProvider.overrideWith((ref) => userPrefs));
    // }
    // if (storage != null) {
    //   overrides.add(storageInfoProvider.overrideWith((ref) => storage));
    // }

    return overrides;
  }
}

/// Mock services for testing
class MockServices {
  // Mock AI service
  // static MockAiService createMockAiService() {
  //   final mock = MockAiService();
  //   when(mock.sendMessage(any)).thenAnswer((_) async => 'Mock AI response');
  //   return mock;
  // }

  // Mock book service
  // static MockBookService createMockBookService() {
  //   final mock = MockBookService();
  //   when(mock.getBooks()).thenAnswer((_) async => MockData.mockBooks);
  //   return mock;
  // }

  // Mock dictionary service
  // static MockDictionaryService createMockDictionaryService() {
  //   final mock = MockDictionaryService();
  //   when(mock.lookup(any)).thenAnswer((_) async => MockDictionaryEntry());
  //   return mock;
  // }

  // Mock TTS service
  // static MockTtsService createMockTtsService() {
  //   final mock = MockTtsService();
  //   when(mock.speak(any)).thenAnswer((_) async => true);
  //   return mock;
  // }
}

/// Test-specific provider states
class TestProviderStates {
  /// Loading state for async providers
  static AsyncValue<T> loading<T>() => const AsyncValue.loading();

  /// Error state for async providers
  static AsyncValue<T> error<T>(Object error, [StackTrace? stackTrace]) =>
      AsyncValue.error(error, stackTrace ?? StackTrace.current);

  /// Success state for async providers
  static AsyncValue<T> data<T>(T value) => AsyncValue.data(value);

  /// Create a mock async provider with different states
  static Override mockAsyncProvider<T>(
    AutoDisposeAsyncNotifierProvider<dynamic, T> provider,
    AsyncValue<T> state,
  ) {
    return provider.overrideWith(() => MockAsyncNotifier<T>(state));
  }
}

/// Mock async notifier for testing
class MockAsyncNotifier<T> extends AutoDisposeAsyncNotifier<T> {
  final AsyncValue<T> _state;

  MockAsyncNotifier(this._state);

  @override
  FutureOr<T> build() {
    return _state.when(
      data: (data) => data,
      loading: () => throw const AsyncLoading<T>(),
      error: (error, stackTrace) => throw AsyncError<T>(error, stackTrace),
    );
  }

  /// Simulate state changes for testing
  void simulateStateChange(AsyncValue<T> newState) {
    state = newState;
  }
}

/// Mock state notifier for testing
class MockStateNotifier<T> extends StateNotifier<T> {
  MockStateNotifier(T initialState) : super(initialState);

  /// Update state for testing
  void updateState(T newState) {
    state = newState;
  }

  /// Simulate async operation
  Future<void> simulateAsyncOperation(T newState) async {
    await Future.delayed(const Duration(milliseconds: 100));
    state = newState;
  }
}

/// Provider container for testing
class TestProviderContainer {
  late final ProviderContainer _container;

  TestProviderContainer({List<Override>? overrides}) {
    _container = ProviderContainer(overrides: overrides ?? []);
  }

  /// Get provider value
  T read<T>(ProviderListenable<T> provider) {
    return _container.read(provider);
  }

  /// Listen to provider changes
  void listen<T>(
    ProviderListenable<T> provider,
    void Function(T? previous, T next) listener,
  ) {
    _container.listen(provider, listener);
  }

  /// Dispose container
  void dispose() {
    _container.dispose();
  }
}

/// Test utilities for provider testing
class ProviderTestUtils {
  /// Test provider state changes
  static Future<void> testProviderStateChange<T>(
    ProviderContainer container,
    StateNotifierProvider<dynamic, T> provider,
    void Function() action,
    T expectedState,
  ) async {
    final notifier = container.read(provider.notifier);
    action();
    await Future.delayed(const Duration(milliseconds: 10));
    expect(container.read(provider), equals(expectedState));
  }

  /// Test async provider loading states
  static Future<void> testAsyncProviderStates<T>(
    ProviderContainer container,
    AutoDisposeAsyncNotifierProvider<dynamic, T> provider,
    Future<void> Function() asyncOperation,
  ) async {
    // Initial state should be loading
    expect(container.read(provider), isA<AsyncLoading<T>>());

    // Perform async operation
    await asyncOperation();

    // Should have data or error
    final state = container.read(provider);
    expect(state.hasValue || state.hasError, isTrue);
  }

  /// Test provider dependencies
  static void testProviderDependencies<T>(
    ProviderContainer container,
    Provider<T> provider,
    List<ProviderBase> expectedDependencies,
  ) {
    // This would test that the provider depends on expected providers
    // Implementation depends on Riverpod internals
  }
}
