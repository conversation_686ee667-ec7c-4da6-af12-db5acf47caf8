/// Widget test helpers for DassoShu Reader
/// 
/// Provides utilities for testing widgets with proper setup, mocking,
/// and platform-specific behavior validation.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../test_config.dart';

/// Widget test helpers and utilities
class WidgetTestHelpers {
  /// Create a test app with proper providers and theming
  static Widget createTestApp({
    required Widget child,
    List<Override>? providerOverrides,
    ThemeData? theme,
    Locale? locale,
  }) {
    return ProviderScope(
      overrides: providerOverrides ?? [],
      child: MaterialApp(
        theme: theme ?? ThemeData(useMaterial3: true),
        locale: locale,
        home: Scaffold(body: child),
        // Add localization delegates if needed
        localizationsDelegates: const [
          // Add your localization delegates here
        ],
        supportedLocales: const [
          Locale('en', 'US'),
          Locale('zh', 'CN'),
          Locale('zh', 'TW'),
        ],
      ),
    );
  }

  /// Pump widget with platform-specific setup
  static Future<void> pumpWidgetWithPlatform(
    WidgetTester tester,
    Widget widget, {
    List<Override>? providerOverrides,
  }) async {
    await tester.pumpWidget(
      createTestApp(
        child: widget,
        providerOverrides: providerOverrides,
      ),
    );
    await TestUtils.pumpAndSettleWithPlatform(tester);
  }

  /// Test widget with different screen sizes
  static Future<void> testWithMultipleScreenSizes(
    String description,
    Widget widget,
    Future<void> Function(WidgetTester tester, Size screenSize) testFunction,
  ) async {
    final screenSizes = [
      const Size(360, 640),   // Small phone
      const Size(412, 892),   // Medium phone (Pixel 5)
      const Size(428, 926),   // Large phone (iPhone 12 Pro Max)
      const Size(768, 1024),  // Tablet portrait
      const Size(1024, 768),  // Tablet landscape
    ];

    group(description, () {
      for (final size in screenSizes) {
        testWidgets('on ${size.width}x${size.height}', (tester) async {
          tester.binding.window.physicalSizeTestValue = size;
          tester.binding.window.devicePixelRatioTestValue = 1.0;
          
          await pumpWidgetWithPlatform(tester, widget);
          await testFunction(tester, size);
          
          // Reset window size
          tester.binding.window.clearPhysicalSizeTestValue();
          tester.binding.window.clearDevicePixelRatioTestValue();
        });
      }
    });
  }

  /// Test widget with different text scales
  static Future<void> testWithTextScaling(
    String description,
    Widget widget,
    Future<void> Function(WidgetTester tester, double textScale) testFunction,
  ) async {
    final textScales = [0.8, 1.0, 1.2, 1.5, 2.0];

    group(description, () {
      for (final scale in textScales) {
        testWidgets('with text scale $scale', (tester) async {
          await tester.pumpWidget(
            MediaQuery(
              data: MediaQueryData(textScaleFactor: scale),
              child: createTestApp(child: widget),
            ),
          );
          await TestUtils.pumpAndSettleWithPlatform(tester);
          await testFunction(tester, scale);
        });
      }
    });
  }

  /// Find widget by semantic label
  static Finder findBySemanticLabel(String label) {
    return find.bySemanticsLabel(label);
  }

  /// Find widget by tooltip
  static Finder findByTooltip(String tooltip) {
    return find.byTooltip(tooltip);
  }

  /// Verify widget accessibility
  static void verifyAccessibility(WidgetTester tester, Finder finder) {
    final widget = tester.widget(finder);
    final renderObject = tester.renderObject(finder);
    
    // Check for semantic labels
    if (widget is Semantics) {
      expect(widget.properties.label, isNotNull);
    }
    
    // Check minimum touch target size
    if (renderObject is RenderBox) {
      expect(renderObject.size.width, greaterThanOrEqualTo(44.0));
      expect(renderObject.size.height, greaterThanOrEqualTo(44.0));
    }
  }

  /// Test widget state changes
  static Future<void> testStateChange<T>(
    WidgetTester tester,
    Finder finder,
    Future<void> Function() action,
    T Function() getState,
    T expectedState,
  ) async {
    await action();
    await tester.pumpAndSettle();
    expect(getState(), equals(expectedState));
  }

  /// Test widget animations
  static Future<void> testAnimation(
    WidgetTester tester,
    Finder finder,
    Duration expectedDuration,
  ) async {
    final controller = AnimationController.unbounded(
      vsync: tester.binding,
    );
    
    // Start animation
    controller.forward();
    
    // Test animation progress
    await tester.pump();
    await tester.pump(expectedDuration ~/ 2);
    
    // Verify animation is in progress
    expect(controller.isAnimating, isTrue);
    
    // Complete animation
    await tester.pumpAndSettle();
    expect(controller.isCompleted, isTrue);
    
    controller.dispose();
  }

  /// Test widget performance
  static Future<void> testWidgetPerformance(
    WidgetTester tester,
    Widget widget,
    String testName,
  ) async {
    final stopwatch = Stopwatch()..start();
    
    await pumpWidgetWithPlatform(tester, widget);
    
    stopwatch.stop();
    final buildTime = stopwatch.elapsedMilliseconds;
    
    debugPrint('Widget build time [$testName]: ${buildTime}ms');
    expect(buildTime, lessThan(100), reason: 'Widget build took too long');
  }

  /// Test widget with different themes
  static Future<void> testWithThemes(
    String description,
    Widget widget,
    Future<void> Function(WidgetTester tester, ThemeData theme) testFunction,
  ) async {
    final themes = {
      'Light Theme': ThemeData(
        useMaterial3: true,
        brightness: Brightness.light,
      ),
      'Dark Theme': ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
      ),
    };

    group(description, () {
      themes.forEach((name, theme) {
        testWidgets('with $name', (tester) async {
          await tester.pumpWidget(
            createTestApp(child: widget, theme: theme),
          );
          await TestUtils.pumpAndSettleWithPlatform(tester);
          await testFunction(tester, theme);
        });
      });
    });
  }

  /// Test widget with different locales
  static Future<void> testWithLocales(
    String description,
    Widget widget,
    Future<void> Function(WidgetTester tester, Locale locale) testFunction,
  ) async {
    final locales = [
      const Locale('en', 'US'),
      const Locale('zh', 'CN'),
      const Locale('zh', 'TW'),
    ];

    group(description, () {
      for (final locale in locales) {
        testWidgets('with locale ${locale.toString()}', (tester) async {
          await tester.pumpWidget(
            createTestApp(child: widget, locale: locale),
          );
          await TestUtils.pumpAndSettleWithPlatform(tester);
          await testFunction(tester, locale);
        });
      }
    });
  }

  /// Verify widget golden file
  static Future<void> verifyGolden(
    WidgetTester tester,
    Widget widget,
    String goldenName,
  ) async {
    await pumpWidgetWithPlatform(tester, widget);
    await expectLater(
      find.byWidget(widget),
      matchesGoldenFile(TestUtils.goldenFileName(goldenName)),
    );
  }

  /// Test widget error handling
  static Future<void> testErrorHandling(
    WidgetTester tester,
    Widget widget,
    void Function() triggerError,
  ) async {
    await pumpWidgetWithPlatform(tester, widget);
    
    // Trigger error
    triggerError();
    await tester.pumpAndSettle();
    
    // Verify error is handled gracefully
    expect(tester.takeException(), isNull);
  }

  /// Test widget loading states
  static Future<void> testLoadingState(
    WidgetTester tester,
    Widget widget,
    Finder loadingIndicatorFinder,
  ) async {
    await pumpWidgetWithPlatform(tester, widget);
    
    // Verify loading indicator is shown
    expect(loadingIndicatorFinder, findsOneWidget);
    
    // Wait for loading to complete
    await tester.pumpAndSettle();
    
    // Verify loading indicator is hidden
    expect(loadingIndicatorFinder, findsNothing);
  }

  /// Test widget scroll behavior
  static Future<void> testScrollBehavior(
    WidgetTester tester,
    Widget widget,
    Finder scrollableFinder,
  ) async {
    await pumpWidgetWithPlatform(tester, widget);
    
    // Test scroll
    await tester.drag(scrollableFinder, const Offset(0, -200));
    await tester.pumpAndSettle();
    
    // Verify scroll occurred
    final scrollable = tester.widget<Scrollable>(scrollableFinder);
    expect(scrollable.controller?.offset, greaterThan(0));
  }
}
